<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selection Bar Debug Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-text {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            user-select: text;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Selection Bar Debug Test</h1>
        
        <div class="debug-info">
            <strong>调试信息：</strong><br>
            1. 打开浏览器开发者工具 (F12)<br>
            2. 查看 Console 标签页<br>
            3. 选择下面的测试文本<br>
            4. 观察控制台输出和 DOM 变化
        </div>
        
        <div class="test-text">
            <strong>测试文本 1：</strong>
            这是一段中文测试文本，请选择这段文字来测试划词工具栏功能。工具栏应该会出现在选中文本的下方。
        </div>
        
        <div class="test-text">
            <strong>Test Text 2:</strong>
            This is an English test paragraph. Select this text to test the selection bar functionality. The toolbar should appear below the selected text.
        </div>
        
        <div class="test-text">
            <strong>测试文本 3：</strong>
            人工智能技术正在快速发展，它将改变我们的生活方式。请选择这段文字测试AI助手的功能。
        </div>
        
        <h2>调试步骤</h2>
        <ol>
            <li>确保插件已安装并启用</li>
            <li>刷新此页面</li>
            <li>打开开发者工具的 Console</li>
            <li>选择上面的任意测试文本</li>
            <li>检查控制台是否有相关日志输出</li>
            <li>检查 DOM 中是否出现了 shadow container</li>
            <li>检查 shadow root 中是否有正确的内容</li>
        </ol>
        
        <h2>预期行为</h2>
        <ul>
            <li>选择文本后，控制台应该输出相关日志</li>
            <li>DOM 中应该出现 id 为 "ai-selection-shadow-container" 的元素</li>
            <li>该元素应该有 shadow root</li>
            <li>shadow root 中应该包含样式和 React 组件</li>
            <li>工具栏应该可见并定位在选中文本下方</li>
        </ul>
        
        <div class="debug-info">
            <strong>常见问题排查：</strong><br>
            - 如果没有日志输出：检查插件是否正确加载<br>
            - 如果有 shadow container 但不可见：检查样式和定位<br>
            - 如果 React 组件没有渲染：检查 React root 创建<br>
            - 如果点击无响应：检查事件处理函数
        </div>
    </div>
    
    <script>
        // 添加一些调试辅助
        console.log('Debug test page loaded');
        
        // 监听选择变化
        document.addEventListener('selectionchange', () => {
            const selection = window.getSelection();
            if (selection && selection.toString().trim()) {
                console.log('Text selected:', selection.toString().trim());
            }
        });
        
        // 检查是否有 shadow containers
        setInterval(() => {
            const containers = document.querySelectorAll('[id*="shadow-container"]');
            if (containers.length > 0) {
                console.log('Found shadow containers:', containers);
            }
        }, 2000);
    </script>
</body>
</html>
