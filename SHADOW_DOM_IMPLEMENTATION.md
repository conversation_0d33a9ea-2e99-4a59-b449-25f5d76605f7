# Shadow DOM + React 实现说明

## 概述

我们已经成功将插件重构为使用 Shadow DOM 和 React 组件的架构，参考了豆包的实现方式。这种架构提供了更好的样式隔离、组件化开发体验和可维护性。

## 架构特点

### 1. Shadow DOM 隔离
- 使用 `attachShadow({ mode: 'open' })` 创建 Shadow Root
- 完全隔离样式，避免与页面样式冲突
- 类似豆包的 `ciciai-shadow-container` 实现

### 2. React 组件化
- 使用 React 18 的 `createRoot` API
- 组件化的 UI 开发，易于维护和扩展
- 支持 TypeScript 类型检查

### 3. 高 z-index 层级
- 使用 `z-index: 2147483647` 确保在最顶层显示
- 参考豆包的层级管理策略

### 4. 模块化文件结构
- 每个组件独立文件夹，包含 `index.tsx` 和 `index.less`
- 使用 Less 预处理器，支持嵌套和变量
- 组件文件夹命名与功能对应，便于维护

### 5. 按需显示逻辑
- 划词工具栏只在选中文本时显示
- 使用 `display: none` 和 `visibility: hidden` 双重隐藏
- 浮动按钮支持拖拽和展开/收起功能

## 文件结构

```
src/contents/
├── components/
│   ├── SelectionBar/           # 划词工具栏组件文件夹
│   │   ├── index.tsx          # 划词工具栏 React 组件
│   │   └── index.less         # 划词工具栏样式 (Less)
│   └── FloatingButton/        # 浮动按钮组件文件夹
│       ├── index.tsx          # 浮动按钮 React 组件
│       └── index.less         # 浮动按钮样式 (Less)
├── scripts/
│   ├── injectSelectionBar.ts   # 划词工具栏管理器
│   ├── injectFloatingButton.ts # 浮动按钮管理器
│   └── injectTranslate.ts      # 翻译功能（保持不变）
└── index.ts                    # 主入口文件
```

## 主要组件

### 1. SelectionBar 组件
- **功能**: 文本选择后显示的工具栏
- **特性**: 
  - 支持总结、翻译、缩写、扩写、润色等功能
  - 下拉菜单展示更多选项
  - 箭头指向选中文本
  - 平滑的动画效果

### 2. FloatingButton 组件
- **功能**: 页面右侧的浮动助手按钮
- **特性**:
  - 可拖拽移动位置
  - 点击展开功能列表
  - 脉冲动画吸引注意
  - 响应式设计

## 技术实现

### Shadow DOM 创建
```typescript
// 创建 shadow container
this.shadowContainer = document.createElement('div');
this.shadowContainer.id = 'ai-selection-shadow-container';
this.shadowContainer.className = 'ai-ext-container';

// 创建 shadow root
this.shadowRoot = this.shadowContainer.attachShadow({ mode: 'open' });

// 添加样式到 shadow root
const style = document.createElement('style');
style.textContent = this.getStyles();
this.shadowRoot.appendChild(style);
```

### React 组件渲染
```typescript
// 创建 React 容器
const reactContainer = document.createElement('div');
this.shadowRoot.appendChild(reactContainer);

// 创建 React root 并渲染组件
this.reactRoot = createRoot(reactContainer);
this.reactRoot.render(
  React.createElement(SelectionBarComponent, {
    selectedText: this.selectedText,
    onAction: this.handleAction.bind(this),
    onClose: this.hideBar.bind(this)
  })
);
```

## 优势

### 1. 样式隔离
- Shadow DOM 完全隔离样式，不会被页面样式影响
- 不会影响页面原有样式
- 更稳定的视觉表现

### 2. 组件化开发
- React 组件易于开发和维护
- 支持状态管理和生命周期
- TypeScript 类型安全

### 3. 可扩展性
- 新功能可以轻松添加为新组件
- 组件间通信通过 props 和回调
- 模块化的代码结构

### 4. 性能优化
- React 的虚拟 DOM 优化渲染
- 按需渲染组件
- 事件委托和优化

## 与豆包的对比

| 特性 | 豆包实现 | 我们的实现 |
|------|----------|------------|
| 容器 | `ciciai-shadow-container` | `ai-selection-shadow-container` |
| 样式隔离 | Shadow DOM | Shadow DOM |
| 组件化 | 原生 HTML/JS | React 组件 |
| 类型安全 | JavaScript | TypeScript |
| 状态管理 | 手动管理 | React 状态 |

## 使用方法

1. 选择页面上的任意文本
2. 划词工具栏会自动出现
3. 点击不同按钮执行相应功能
4. 浮动按钮可以拖拽移动
5. 点击浮动按钮展开更多功能

## 测试

使用提供的 `test.html` 文件进行功能测试：
1. 在浏览器中打开 `test.html`
2. 安装并启用插件
3. 测试文本选择和浮动按钮功能
4. 查看控制台日志确认功能正常

## 后续优化

1. 添加更多 AI 功能
2. 优化动画效果
3. 支持更多语言
4. 添加用户设置面板
5. 集成更多 AI 服务
