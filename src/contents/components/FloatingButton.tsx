import React, { useState, useEffect } from 'react';
import { browser } from 'webextension-polyfill-ts';
import './FloatingButton.css';

interface FloatingButtonProps {
  onAction: (action: string) => void;
}

const FloatingButton: React.FC<FloatingButtonProps> = ({ onAction }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [position, setPosition] = useState({ x: window.innerWidth - 60, y: window.innerHeight / 2 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - position.x,
      y: e.clientY - position.y
    });
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, dragOffset]);

  const toggleExpanded = () => {
    if (!isDragging) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleAction = (action: string) => {
    onAction(action);
    setIsExpanded(false);
  };

  return (
    <div 
      className="floating-button-container"
      style={{
        position: 'fixed',
        left: `${position.x}px`,
        top: `${position.y}px`,
        zIndex: 2147483647,
        transform: 'translate3d(0px, 0px, 0px)'
      }}
    >
      <div className="floating-button-wrapper">
        {isExpanded && (
          <div className="floating-button-list">
            <div className="floating-button-item" onClick={() => handleAction('translate')}>
              <div className="floating-button-icon">
                <svg width="1em" height="1em" fill="none" viewBox="0 0 24 24">
                  <path fill="currentColor" fillRule="evenodd" d="M7.128 1.894a.97.97 0 0 0-1.422 1.26H2.668a.97.97 0 0 0 0 1.94h1.084c.463 1.217 1.359 2.568 2.45 3.77-1.12.958-2.338 1.561-3.534 1.561a.97.97 0 0 0 0 1.94c1.882 0 3.574-.951 4.926-2.143 1.064.915 2.267 1.684 3.493 2.093a.97.97 0 0 0 .613-1.84c-.899-.3-1.851-.888-2.748-1.643 1.07-1.258 1.885-2.64 2.324-3.739h1.087a.97.97 0 0 0 0-1.939H8.808zm2.017 3.2H5.877c.389.75.97 1.582 1.683 2.381.666-.8 1.209-1.646 1.585-2.382" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            
            <div className="floating-button-divider"></div>
            
            <div className="floating-button-item" onClick={() => handleAction('read-page')}>
              <div className="floating-button-icon">
                <svg width="1em" height="1em" fill="none" viewBox="0 0 24 24">
                  <path fill="currentColor" fillRule="evenodd" d="M20.014 9.046a.514.514 0 0 1-.512.454.52.52 0 0 1-.515-.452c-.241-1.445-.452-2.18-.906-2.634-.455-.453-1.186-.66-2.612-.9A.52.52 0 0 1 15 5.002c0-.275.206-.487.473-.513 1.422-.242 2.153-.454 2.607-.91s.666-1.191.907-2.623A.52.52 0 0 1 19.502.5c.273 0 .473.197.512.458.24 1.43.451 2.165.906 2.622.455.455 1.187.667 2.61.909.266.************.513a.52.52 0 0 1-.468.512c-1.425.24-2.157.452-2.612.907-.455.456-.666 1.192-.907 2.625" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            
            <div className="floating-button-item" onClick={() => handleAction('screenshot')}>
              <div className="floating-button-icon">
                <svg width="1em" height="1em" fill="none" viewBox="0 0 24 24">
                  <path fill="currentColor" fillRule="evenodd" d="M18.042 1.706a1 1 0 0 0-1.392.245l-4.74 6.77-4.74-6.77A1 1 0 0 0 5.53 3.098l5.158 7.366-2.715 3.878a4.002 4.002 0 0 0-3.697 7.023 4 4 0 0 0 5.335-5.876l2.297-3.281 2.298 3.28a4.002 4.002 0 0 0 5.335 5.877 4 4 0 0 0-3.697-7.023l-2.715-3.878 5.158-7.366a1 1 0 0 0-.246-1.392" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
            
            <div className="floating-button-item" onClick={() => handleAction('collect')}>
              <div className="floating-button-icon">
                <svg width="1em" height="1em" fill="none" viewBox="0 0 24 24">
                  <path fill="currentColor" fillRule="evenodd" d="M18.126 3.118H5.874l.012-.248c.022-.426.17-.788.448-1.042.278-.253.658-.374 1.097-.374h9.136c.44 0 .82.121 1.097.374.279.254.427.616.448 1.042zm1.925 2.706-.305-.049a6 6 0 0 0-.915-.068H5.17q-.485 0-.914.068l-.306.05.035-.31c.056-.496.256-.905.6-1.187.34-.28.797-.415 1.33-.415h12.182c.533 0 .988.134 1.327.415.341.283.539.692.594 1.188z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>
          </div>
        )}
        
        <div 
          className="floating-button-brand"
          onMouseDown={handleMouseDown}
          onClick={toggleExpanded}
        >
          <img 
            src={browser.runtime.getURL('assets/icon.png')} 
            className="floating-button-avatar" 
            alt="AI助手"
          />
        </div>
      </div>
    </div>
  );
};

export default FloatingButton;
