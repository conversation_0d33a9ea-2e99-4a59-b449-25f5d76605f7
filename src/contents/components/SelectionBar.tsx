import React, { useState } from 'react';
import { browser } from 'webextension-polyfill-ts';
import './SelectionBar.css';

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleAction = (action: string) => {
    onAction(action);
    if (action !== 'open-panel') {
      onClose();
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  return (
    <div className="ai-selection-bar">
      <div className="ai-selection-icon" onClick={() => handleAction('open-panel')} title="打开插件面板">
        <img src={browser.runtime.getURL('assets/icon.png')} alt="AI助手" />
      </div>
      
      <div className="ai-selection-button" onClick={() => handleAction('summary')}>
        总结
      </div>
      
      <div className="ai-selection-button" onClick={() => handleAction('translate')}>
        翻译
      </div>
      
      <div className="ai-selection-more">
        <div className="ai-selection-dots" onClick={toggleDropdown}>
          ⋮
        </div>
        {showDropdown && (
          <div className="ai-selection-dropdown show">
            <div className="ai-selection-dropdown-item" onClick={() => handleAction('abbreviate')}>
              缩写
            </div>
            <div className="ai-selection-dropdown-item" onClick={() => handleAction('expand')}>
              扩写
            </div>
            <div className="ai-selection-dropdown-item" onClick={() => handleAction('polish')}>
              润色
            </div>
            <div className="ai-selection-dropdown-item" onClick={() => handleAction('correct')}>
              修正拼写和语义
            </div>
          </div>
        )}
      </div>
      
      <div className="ai-selection-close" onClick={onClose} title="关闭">
        ×
      </div>
    </div>
  );
};

export default SelectionBar;
