import React, { useState } from 'react';
import { browser } from 'webextension-polyfill-ts';

interface SelectionBarProps {
  selectedText: string;
  onAction: (action: string) => void;
  onClose: () => void;
}

const SelectionBar: React.FC<SelectionBarProps> = ({ selectedText, onAction, onClose }) => {
  const [showDropdown, setShowDropdown] = useState(false);

  console.log('SelectionBar component rendered with text:', selectedText);

  const handleAction = (action: string) => {
    console.log('SelectionBar action:', action);
    onAction(action);
    if (action !== 'open-panel') {
      onClose();
    }
  };

  const toggleDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(!showDropdown);
  };

  return (
    <div style={{ position: 'relative' }}>
      {/* 箭头指向选中文本 */}
      <div
        style={{
          position: 'absolute',
          top: '-8px',
          left: '20px',
          width: '0',
          height: '0',
          borderLeft: '8px solid transparent',
          borderRight: '8px solid transparent',
          borderBottom: '8px solid white',
          filter: 'drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1))'
        }}
      />

      <div
        className="ai-selection-bar"
        style={{
          position: 'relative',
          display: 'flex',
          alignItems: 'center',
          backgroundColor: 'white',
          borderRadius: '6px',
          boxShadow: '0 4px 15px rgba(0, 0, 0, 0.3)',
          padding: '8px 12px',
          zIndex: 9999,
          fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
          fontSize: '14px',
          border: '1px solid #e0e0e0',
          opacity: 1,
          transform: 'translateY(0)'
        }}
      >
      <div
        className="ai-selection-icon"
        onClick={() => handleAction('open-panel')}
        title="打开插件面板"
        style={{
          width: '20px',
          height: '20px',
          marginRight: '10px',
          cursor: 'pointer'
        }}
      >
        <img
          src={browser.runtime.getURL('assets/icon.png')}
          alt="AI助手"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'contain' as const
          }}
        />
      </div>

      <div
        className="ai-selection-button"
        onClick={() => handleAction('summary')}
        style={{
          padding: '4px 10px',
          margin: '0 5px',
          cursor: 'pointer',
          borderRadius: '4px',
          transition: 'all 0.2s ease',
          color: '#333',
          fontWeight: '500'
        }}
      >
        总结
      </div>

      <div
        className="ai-selection-button"
        onClick={() => handleAction('translate')}
        style={{
          padding: '4px 10px',
          margin: '0 5px',
          cursor: 'pointer',
          borderRadius: '4px',
          transition: 'all 0.2s ease',
          color: '#333',
          fontWeight: '500'
        }}
      >
        翻译
      </div>
      
      <div
        className="ai-selection-more"
        style={{
          position: 'relative',
          margin: '0 4px'
        }}
      >
        <div
          className="ai-selection-dots"
          onClick={toggleDropdown}
          style={{
            cursor: 'pointer',
            padding: '0 5px',
            fontWeight: 'bold',
            fontSize: '16px'
          }}
        >
          ⋮
        </div>
        {showDropdown && (
          <div
            className="ai-selection-dropdown show"
            style={{
              position: 'absolute',
              top: '100%',
              right: '0',
              backgroundColor: 'white',
              boxShadow: '0 2px 10px rgba(0, 0, 0, 0.2)',
              borderRadius: '4px',
              minWidth: '120px',
              zIndex: 10000
            }}
          >
            <div
              className="ai-selection-dropdown-item"
              onClick={() => handleAction('abbreviate')}
              style={{
                padding: '8px 12px',
                cursor: 'pointer'
              }}
            >
              缩写
            </div>
            <div
              className="ai-selection-dropdown-item"
              onClick={() => handleAction('expand')}
              style={{
                padding: '8px 12px',
                cursor: 'pointer'
              }}
            >
              扩写
            </div>
            <div
              className="ai-selection-dropdown-item"
              onClick={() => handleAction('polish')}
              style={{
                padding: '8px 12px',
                cursor: 'pointer'
              }}
            >
              润色
            </div>
            <div
              className="ai-selection-dropdown-item"
              onClick={() => handleAction('correct')}
              style={{
                padding: '8px 12px',
                cursor: 'pointer'
              }}
            >
              修正拼写和语义
            </div>
          </div>
        )}
      </div>

      <div
        className="ai-selection-close"
        onClick={onClose}
        title="关闭"
        style={{
          marginLeft: '10px',
          cursor: 'pointer',
          fontSize: '16px',
          width: '20px',
          height: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '50%'
        }}
      >
        ×
      </div>
      </div>
    </div>
  );
};

export default SelectionBar;
