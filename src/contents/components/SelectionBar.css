.ai-selection-bar {
  position: absolute;
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 6px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  z-index: 9999;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-size: 14px;
  transition: opacity 0.2s ease, transform 0.2s ease;
  border: 1px solid #e0e0e0;
  opacity: 1;
  transform: translateY(0);
}

.ai-selection-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  cursor: pointer;
}

.ai-selection-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.ai-selection-button {
  padding: 4px 10px;
  margin: 0 5px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
  color: #333;
  font-weight: 500;
}

.ai-selection-button:hover {
  background-color: #f0f0f0;
  transform: translateY(-1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.ai-selection-more {
  position: relative;
  margin: 0 4px;
}

.ai-selection-dots {
  cursor: pointer;
  padding: 0 5px;
  font-weight: bold;
  font-size: 16px;
}

.ai-selection-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  min-width: 120px;
  z-index: 10000;
}

.ai-selection-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
}

.ai-selection-dropdown-item:hover {
  background-color: #f0f0f0;
}

.ai-selection-close {
  margin-left: 10px;
  cursor: pointer;
  font-size: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.ai-selection-close:hover {
  background-color: #f0f0f0;
}

/* 添加指向选中文本的箭头 */
.ai-selection-bar::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid white;
  filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
}
