import { browser } from 'webextension-polyfill-ts';

class SelectionBar {
  private bar: HTMLDivElement | null = null;
  private selectedText: string = '';
  private isBarVisible: boolean = false;

  constructor() {
    this.init();
  }

  private init(): void {
    console.log('SelectionBar init');
    
    document.addEventListener('mouseup', this.handleMouseUp);
    document.addEventListener('click', this.handleDocumentClick);
  }

  private handleMouseUp = (event: MouseEvent): void => {
    const selection = window.getSelection();
    if (!selection) return;
    
    this.selectedText = selection.toString().trim();
    
    if (this.selectedText) {
      this.showBar(event);
    } else if (this.isBarVisible) {
      this.hideBar();
    }
  };

  private handleDocumentClick = (event: MouseEvent): void => {
    if (this.isBarVisible && this.bar && !this.bar.contains(event.target as Node)) {
      this.hideBar();
    }
  };

  private showBar(event: MouseEvent): void {
    if (!this.selectedText) return;
    
    if (!this.bar) {
      this.createBar();
    }
    
    if (this.bar) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        
        // 确保工具栏显示在选中文本的正下方
        this.bar.style.left = `${rect.left + window.scrollX}px`;
        this.bar.style.top = `${rect.bottom + window.scrollY + 5}px`;
        this.bar.style.display = 'flex';
        
        // 添加过渡效果使其更明显
        this.bar.style.opacity = '0';
        this.bar.style.transform = 'translateY(-10px)';
        
        // 使用setTimeout确保CSS过渡效果生效
        setTimeout(() => {
          if (this.bar) {
            this.bar.style.opacity = '1';
            this.bar.style.transform = 'translateY(0)';
          }
        }, 10);
        
        this.isBarVisible = true;
      }
    }
  }

  private hideBar(): void {
    if (this.bar) {
      this.bar.style.display = 'none';
      this.isBarVisible = false;
    }
  }

  private createBar(): void {
    this.bar = document.createElement('div');
    this.bar.className = 'ai-selection-bar';
    this.bar.innerHTML = `
      <div class="ai-selection-icon" title="打开插件面板">
        <img src="${browser.runtime.getURL('assets/icon.png')}" alt="AI助手" />
      </div>
      <div class="ai-selection-button" data-action="summary">总结</div>
      <div class="ai-selection-button" data-action="translate">翻译</div>
      <div class="ai-selection-more">
        <div class="ai-selection-dots">⋮</div>
        <div class="ai-selection-dropdown">
          <div class="ai-selection-dropdown-item" data-action="abbreviate">缩写</div>
          <div class="ai-selection-dropdown-item" data-action="expand">扩写</div>
          <div class="ai-selection-dropdown-item" data-action="polish">润色</div>
          <div class="ai-selection-dropdown-item" data-action="correct">修正拼写和语义</div>
        </div>
      </div>
      <div class="ai-selection-close" title="关闭">×</div>
    `;
    
    document.body.appendChild(this.bar);
    
    // 添加事件监听器
    const iconElement = this.bar.querySelector('.ai-selection-icon');
    const closeElement = this.bar.querySelector('.ai-selection-close');
    const moreElement = this.bar.querySelector('.ai-selection-more');
    const buttons = this.bar.querySelectorAll('.ai-selection-button, .ai-selection-dropdown-item');
    
    if (iconElement) {
      iconElement.addEventListener('click', () => this.handleAction('open-panel'));
    }
    
    if (closeElement) {
      closeElement.addEventListener('click', () => this.hideBar());
    }
    
    if (moreElement) {
      moreElement.addEventListener('click', (e) => {
        const dropdown = moreElement.querySelector('.ai-selection-dropdown');
        if (dropdown) {
          dropdown.classList.toggle('show');
          e.stopPropagation();
        }
      });
    }
    
    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        const action = (e.currentTarget as HTMLElement).getAttribute('data-action');
        if (action) {
          this.handleAction(action);
        }
      });
    });
    
    // 添加样式
    this.addStyles();
  }

  private handleAction(action: string): void {
    // 发送消息到background脚本
    browser.runtime.sendMessage({
      action: action,
      text: this.selectedText
    }).catch(error => console.error('发送消息失败:', error));
    
    // 对于某些操作，可能需要隐藏工具栏
    if (action !== 'open-panel') {
      this.hideBar();
    }
  }

  private addStyles(): void {
    const style = document.createElement('style');
    style.textContent = `
      .ai-selection-bar {
        position: absolute;
        display: none;
        align-items: center;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        padding: 8px 12px;
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 14px;
        transition: opacity 0.2s ease, transform 0.2s ease;
        border: 1px solid #e0e0e0;
      }
      
      .ai-selection-icon {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        cursor: pointer;
      }
      
      .ai-selection-icon img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      
      .ai-selection-button {
        padding: 4px 10px;
        margin: 0 5px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s ease;
        color: #333;
        font-weight: 500;
      }
      
      .ai-selection-button:hover {
        background-color: #f0f0f0;
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }
      
      .ai-selection-more {
        position: relative;
        margin: 0 4px;
      }
      
      .ai-selection-dots {
        cursor: pointer;
        padding: 0 5px;
        font-weight: bold;
        font-size: 16px;
      }
      
      .ai-selection-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        display: none;
        min-width: 120px;
        z-index: 10000;
      }
      
      .ai-selection-dropdown.show {
        display: block;
      }
      
      .ai-selection-dropdown-item {
        padding: 8px 12px;
        cursor: pointer;
      }
      
      .ai-selection-dropdown-item:hover {
        background-color: #f0f0f0;
      }
      
      .ai-selection-close {
        margin-left: 10px;
        cursor: pointer;
        font-size: 16px;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
      }
      
      .ai-selection-close:hover {
        background-color: #f0f0f0;
      }
      
      /* 添加指向选中文本的箭头 */
      .ai-selection-bar::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 20px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid white;
        filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
      }
    `;
    document.head.appendChild(style);
  }
}

// 初始化选择工具栏
new SelectionBar();