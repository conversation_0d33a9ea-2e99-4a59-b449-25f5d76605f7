import React from 'react';
import { createRoot } from 'react-dom/client';
import { browser } from 'webextension-polyfill-ts';
import SelectionBarComponent from '../components/SelectionBar';

class SelectionBar {
  private shadowContainer: HTMLDivElement | null = null;
  private shadowRoot: ShadowRoot | null = null;
  private reactRoot: any = null;
  private selectedText: string = '';
  private isBarVisible: boolean = false;

  constructor() {
    this.init();
  }

  private init(): void {
    console.log('SelectionBar init');

    document.addEventListener('mouseup', this.handleMouseUp);
    document.addEventListener('click', this.handleDocumentClick);
  }

  private handleMouseUp = (event: MouseEvent): void => {
    const selection = window.getSelection();
    if (!selection) return;

    this.selectedText = selection.toString().trim();

    if (this.selectedText) {
      this.showBar(event);
    } else if (this.isBarVisible) {
      this.hideBar();
    }
  };

  private handleDocumentClick = (event: MouseEvent): void => {
    if (this.isBarVisible && this.shadowContainer && !this.shadowContainer.contains(event.target as Node)) {
      this.hideBar();
    }
  };

  private showBar(_event: MouseEvent): void {
    if (!this.selectedText) return;

    if (!this.shadowContainer) {
      this.createShadowContainer();
    }

    if (this.shadowContainer && this.reactRoot) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();

        // 确保工具栏显示在选中文本的正下方
        this.shadowContainer.style.left = `${rect.left + window.scrollX}px`;
        this.shadowContainer.style.top = `${rect.bottom + window.scrollY + 5}px`;
        this.shadowContainer.style.visibility = 'visible';

        // 渲染 React 组件
        this.reactRoot.render(
          React.createElement(SelectionBarComponent, {
            selectedText: this.selectedText,
            onAction: this.handleAction.bind(this),
            onClose: this.hideBar.bind(this)
          })
        );

        this.isBarVisible = true;
      }
    }
  }

  private hideBar(): void {
    if (this.shadowContainer) {
      this.shadowContainer.style.visibility = 'hidden';
      this.isBarVisible = false;
    }
  }

  private createShadowContainer(): void {
    // 创建 shadow container
    this.shadowContainer = document.createElement('div');
    this.shadowContainer.id = 'ai-selection-shadow-container';
    this.shadowContainer.className = 'ai-ext-container';
    this.shadowContainer.style.cssText = `
      z-index: 2147483647;
      position: absolute;
      top: 0px;
      left: 0px;
      visibility: hidden;
    `;

    // 创建 shadow root
    this.shadowRoot = this.shadowContainer.attachShadow({ mode: 'open' });

    // 创建样式元素并添加到 shadow root
    const style = document.createElement('style');
    style.textContent = this.getStyles();
    this.shadowRoot.appendChild(style);

    // 创建 React 容器
    const reactContainer = document.createElement('div');
    this.shadowRoot.appendChild(reactContainer);

    // 创建 React root
    this.reactRoot = createRoot(reactContainer);

    // 添加到页面
    document.body.appendChild(this.shadowContainer);
  }

  private handleAction(action: string): void {
    // 发送消息到background脚本
    browser.runtime.sendMessage({
      action: action,
      text: this.selectedText
    }).catch(error => console.error('发送消息失败:', error));
    
    // 对于某些操作，可能需要隐藏工具栏
    if (action !== 'open-panel') {
      this.hideBar();
    }
  }

  private getStyles(): string {
    return `
      .ai-selection-bar {
        position: absolute;
        display: flex;
        align-items: center;
        background-color: white;
        border-radius: 6px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        padding: 8px 12px;
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        font-size: 14px;
        transition: opacity 0.2s ease, transform 0.2s ease;
        border: 1px solid #e0e0e0;
        opacity: 1;
        transform: translateY(0);
      }

      .ai-selection-icon {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        cursor: pointer;
      }

      .ai-selection-icon img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .ai-selection-button {
        padding: 4px 10px;
        margin: 0 5px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s ease;
        color: #333;
        font-weight: 500;
      }

      .ai-selection-button:hover {
        background-color: #f0f0f0;
        transform: translateY(-1px);
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      .ai-selection-more {
        position: relative;
        margin: 0 4px;
      }

      .ai-selection-dots {
        cursor: pointer;
        padding: 0 5px;
        font-weight: bold;
        font-size: 16px;
      }

      .ai-selection-dropdown {
        position: absolute;
        top: 100%;
        right: 0;
        background-color: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        min-width: 120px;
        z-index: 10000;
      }

      .ai-selection-dropdown-item {
        padding: 8px 12px;
        cursor: pointer;
      }

      .ai-selection-dropdown-item:hover {
        background-color: #f0f0f0;
      }

      .ai-selection-close {
        margin-left: 10px;
        cursor: pointer;
        font-size: 16px;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
      }

      .ai-selection-close:hover {
        background-color: #f0f0f0;
      }

      /* 添加指向选中文本的箭头 */
      .ai-selection-bar::before {
        content: '';
        position: absolute;
        top: -8px;
        left: 20px;
        width: 0;
        height: 0;
        border-left: 8px solid transparent;
        border-right: 8px solid transparent;
        border-bottom: 8px solid white;
        filter: drop-shadow(0 -2px 2px rgba(0, 0, 0, 0.1));
      }
    `;
  }
}

// 初始化选择工具栏
new SelectionBar();