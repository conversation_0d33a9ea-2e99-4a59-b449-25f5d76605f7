import React from 'react';
import { createRoot } from 'react-dom/client';
import { browser } from 'webextension-polyfill-ts';
import FloatingButtonComponent from '../components/FloatingButton/index';

class FloatingButtonManager {
  private shadowContainer: HTMLDivElement | null = null;
  private shadowRoot: ShadowRoot | null = null;
  private reactRoot: any = null;
  private isVisible: boolean = true;

  constructor() {
    this.init();
  }

  private init(): void {
    console.log('FloatingButton init');
    this.createShadowContainer();
    this.renderFloatingButton();
  }

  private createShadowContainer(): void {
    // 创建 shadow container，类似豆包的实现
    this.shadowContainer = document.createElement('div');
    this.shadowContainer.id = 'ai-floating-shadow-container';
    this.shadowContainer.className = 'ai-ext-container ai-ext-always-light';
    this.shadowContainer.style.cssText = `
      z-index: 2147483647;
      position: fixed;
      top: 0px;
      left: 0px;
      visibility: visible;
      pointer-events: none;
    `;
    
    // 创建 shadow root
    this.shadowRoot = this.shadowContainer.attachShadow({ mode: 'open' });
    
    // 创建样式元素并添加到 shadow root
    const style = document.createElement('style');
    style.textContent = this.getStyles();
    this.shadowRoot.appendChild(style);
    
    // 创建 React 容器
    const reactContainer = document.createElement('div');
    reactContainer.style.pointerEvents = 'auto';
    this.shadowRoot.appendChild(reactContainer);
    
    // 创建 React root
    this.reactRoot = createRoot(reactContainer);
    
    // 添加到页面
    document.body.appendChild(this.shadowContainer);
  }

  private renderFloatingButton(): void {
    if (this.reactRoot) {
      this.reactRoot.render(
        React.createElement(FloatingButtonComponent, {
          onAction: this.handleAction.bind(this)
        })
      );
    }
  }

  private handleAction(action: string): void {
    console.log('FloatingButton action:', action);
    
    // 发送消息到background脚本
    browser.runtime.sendMessage({
      action: action,
      source: 'floating-button'
    }).catch(error => console.error('发送消息失败:', error));
  }

  private getStyles(): string {
    return `
      .floating-button-container {
        position: fixed;
        z-index: 2147483647;
        user-select: none;
      }

      .floating-button-wrapper {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        width: 40px;
      }

      .floating-button-list {
        display: flex;
        flex-direction: column;
        align-items: center;
        background: white;
        border-radius: 20px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        padding: 8px;
        margin-bottom: 8px;
        animation: slideIn 0.2s ease-out;
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .floating-button-item {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;
        margin: 2px 0;
      }

      .floating-button-item:hover {
        background-color: #f5f5f5;
        transform: scale(1.1);
      }

      .floating-button-icon {
        width: 20px;
        height: 20px;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .floating-button-icon svg {
        width: 100%;
        height: 100%;
      }

      .floating-button-divider {
        width: 24px;
        height: 1px;
        background-color: #e0e0e0;
        margin: 4px 0;
      }

      .floating-button-brand {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        transition: all 0.3s ease;
        position: relative;
      }

      .floating-button-brand:hover {
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
      }

      .floating-button-brand:active {
        transform: scale(0.95);
      }

      .floating-button-avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        object-fit: cover;
      }

      .floating-button-brand::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        opacity: 0;
        animation: pulse 2s infinite;
      }

      @keyframes pulse {
        0% {
          opacity: 0;
          transform: scale(1);
        }
        50% {
          opacity: 0.3;
          transform: scale(1.1);
        }
        100% {
          opacity: 0;
          transform: scale(1.2);
        }
      }

      @media (max-width: 768px) {
        .floating-button-brand {
          width: 36px;
          height: 36px;
        }
        
        .floating-button-avatar {
          width: 20px;
          height: 20px;
        }
        
        .floating-button-wrapper {
          width: 36px;
        }
      }
    `;
  }

  public show(): void {
    if (this.shadowContainer) {
      this.shadowContainer.style.visibility = 'visible';
      this.isVisible = true;
    }
  }

  public hide(): void {
    if (this.shadowContainer) {
      this.shadowContainer.style.visibility = 'hidden';
      this.isVisible = false;
    }
  }

  public toggle(): void {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }
}

// 初始化浮动按钮
new FloatingButtonManager();
