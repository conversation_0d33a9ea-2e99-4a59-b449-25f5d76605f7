// 简化的测试版本，不使用 Shadow DOM
class TestSelectionBar {
  private bar: HTMLDivElement | null = null;
  private selectedText: string = '';
  private isBarVisible: boolean = false;

  constructor() {
    this.init();
  }

  private init(): void {
    console.log('TestSelectionBar init');
    
    document.addEventListener('mouseup', this.handleMouseUp);
    document.addEventListener('click', this.handleDocumentClick);
  }

  private handleMouseUp = (event: MouseEvent): void => {
    console.log('Test: Mouse up event triggered');
    const selection = window.getSelection();
    if (!selection) {
      console.log('Test: No selection found');
      return;
    }
    
    this.selectedText = selection.toString().trim();
    console.log('Test: Selected text:', this.selectedText);
    
    if (this.selectedText) {
      console.log('Test: Showing selection bar...');
      this.showBar(event);
    } else if (this.isBarVisible) {
      console.log('Test: Hiding selection bar...');
      this.hideBar();
    }
  };

  private handleDocumentClick = (event: MouseEvent): void => {
    if (this.isBarVisible && this.bar && !this.bar.contains(event.target as Node)) {
      this.hideBar();
    }
  };

  private showBar(event: MouseEvent): void {
    if (!this.selectedText) return;
    
    console.log('Test: ShowBar called with text:', this.selectedText);
    
    if (!this.bar) {
      this.createBar();
    }
    
    if (this.bar) {
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const rect = range.getBoundingClientRect();
        
        console.log('Test: Selection rect:', rect);
        
        // 确保工具栏显示在选中文本的正下方
        this.bar.style.left = `${rect.left + window.scrollX}px`;
        this.bar.style.top = `${rect.bottom + window.scrollY + 5}px`;
        this.bar.style.display = 'flex';
        this.bar.style.visibility = 'visible';
        
        console.log('Test: Bar positioned at:', {
          left: this.bar.style.left,
          top: this.bar.style.top,
          display: this.bar.style.display,
          visibility: this.bar.style.visibility
        });
        
        this.isBarVisible = true;
        console.log('Test: SelectionBar should be visible now');
      }
    }
  }

  private hideBar(): void {
    if (this.bar) {
      this.bar.style.display = 'none';
      this.bar.style.visibility = 'hidden';
      this.isBarVisible = false;
      console.log('Test: SelectionBar hidden');
    }
  }

  private createBar(): void {
    console.log('Test: Creating selection bar...');
    
    this.bar = document.createElement('div');
    this.bar.id = 'test-selection-bar';
    this.bar.style.cssText = `
      position: absolute;
      display: none;
      align-items: center;
      background-color: white;
      border-radius: 6px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
      padding: 8px 12px;
      z-index: 2147483647;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      font-size: 14px;
      border: 1px solid #e0e0e0;
      visibility: hidden;
    `;
    
    this.bar.innerHTML = `
      <div style="width: 20px; height: 20px; margin-right: 10px; cursor: pointer; background: #007bff; border-radius: 3px; display: flex; align-items: center; justify-content: center; color: white; font-size: 12px;">AI</div>
      <div style="padding: 4px 10px; margin: 0 5px; cursor: pointer; border-radius: 4px; color: #333; font-weight: 500; background: #f8f9fa;" data-action="summary">总结</div>
      <div style="padding: 4px 10px; margin: 0 5px; cursor: pointer; border-radius: 4px; color: #333; font-weight: 500; background: #f8f9fa;" data-action="translate">翻译</div>
      <div style="margin-left: 10px; cursor: pointer; font-size: 16px; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; border-radius: 50%; background: #f8f9fa;" data-action="close">×</div>
    `;
    
    // 添加事件监听器
    const buttons = this.bar.querySelectorAll('[data-action]');
    buttons.forEach(button => {
      button.addEventListener('click', (e) => {
        const action = (e.currentTarget as HTMLElement).getAttribute('data-action');
        console.log('Test: Button clicked:', action);
        if (action === 'close') {
          this.hideBar();
        } else {
          console.log('Test: Action triggered:', action, 'with text:', this.selectedText);
        }
      });
    });
    
    document.body.appendChild(this.bar);
    console.log('Test: Selection bar created and added to DOM');
  }
}

// 初始化测试选择工具栏
console.log('Test: Initializing TestSelectionBar...');
new TestSelectionBar();
