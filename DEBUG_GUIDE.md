# 划词工具栏调试指南

## 当前状态

我们已经创建了两个版本的划词工具栏：

1. **Shadow DOM + React 版本** (`injectSelectionBar.ts` + `SelectionBar.tsx`)
2. **简化测试版本** (`testSelectionBar.ts`)

目前启用的是简化测试版本，用于排查基本功能是否正常。

## 调试步骤

### 第一步：测试基本功能

1. 在浏览器中打开 `debug-test.html`
2. 打开开发者工具 (F12)，切换到 Console 标签
3. 刷新页面，应该看到：
   ```
   Debug test page loaded
   Test: Initializing TestSelectionBar...
   Test: TestSelectionBar init
   ```

4. 选择页面上的任意文本
5. 检查控制台输出，应该看到类似：
   ```
   Text selected: [选中的文本]
   Test: Mouse up event triggered
   Test: Selected text: [选中的文本]
   Test: Showing selection bar...
   Test: ShowBar called with text: [选中的文本]
   Test: Creating selection bar...
   Test: Selection bar created and added to DOM
   Test: Selection rect: DOMRect {...}
   Test: Bar positioned at: {...}
   Test: SelectionBar should be visible now
   ```

6. 检查页面上是否出现了工具栏

### 第二步：检查 DOM 结构

在开发者工具的 Elements 标签中，查找：
- id 为 `test-selection-bar` 的元素
- 该元素应该有正确的样式和位置
- 该元素应该包含按钮

### 第三步：测试交互

1. 点击工具栏上的按钮
2. 检查控制台是否有相应的日志输出
3. 点击关闭按钮，工具栏应该消失

## 问题排查

### 如果测试版本不工作

**问题1：没有控制台输出**
- 检查插件是否正确安装和启用
- 检查 content script 是否正确加载
- 检查页面是否匹配插件的 matches 规则

**问题2：有日志但没有工具栏**
- 检查 DOM 中是否有 `test-selection-bar` 元素
- 检查该元素的样式，特别是 `display` 和 `visibility`
- 检查 z-index 是否足够高

**问题3：工具栏位置不正确**
- 检查 `getBoundingClientRect()` 返回的值
- 检查 `scrollX` 和 `scrollY` 的计算

### 如果测试版本工作，但 Shadow DOM 版本不工作

**问题1：Shadow DOM 创建失败**
- 检查 `attachShadow` 是否成功
- 检查浏览器是否支持 Shadow DOM

**问题2：React 组件未渲染**
- 检查 `createRoot` 是否成功
- 检查 React 组件是否有语法错误
- 检查样式是否正确应用到 Shadow DOM 中

**问题3：样式问题**
- Shadow DOM 中的样式是完全隔离的
- 确保所有必要的样式都注入到了 Shadow Root 中
- 检查内联样式是否正确应用

## 切换到 Shadow DOM 版本

如果测试版本工作正常，可以切换回 Shadow DOM 版本：

1. 编辑 `src/contents/index.ts`：
   ```typescript
   import './scripts/injectSelectionBar' // 启用 Shadow DOM 版本
   // import './scripts/testSelectionBar' // 禁用测试版本
   ```

2. 重新构建插件
3. 重复调试步骤，但查找 `ai-selection-shadow-container` 元素

## 常见解决方案

### 样式不显示
在 Shadow DOM 中，需要确保所有样式都正确注入：
```typescript
const style = document.createElement('style');
style.textContent = this.getStyles();
this.shadowRoot.appendChild(style);
```

### React 组件不渲染
确保 React 容器正确添加到 Shadow Root：
```typescript
const reactContainer = document.createElement('div');
this.shadowRoot.appendChild(reactContainer);
this.reactRoot = createRoot(reactContainer);
```

### 事件处理不工作
在 Shadow DOM 中，事件处理需要特别注意：
- 确保事件监听器正确绑定
- 检查事件冒泡是否被阻止

## 下一步

1. 首先确保测试版本工作正常
2. 如果测试版本正常，逐步调试 Shadow DOM 版本
3. 解决 Shadow DOM 版本的问题后，重新启用浮动按钮功能

## 调试工具

使用以下代码片段在控制台中检查状态：

```javascript
// 检查是否有选择工具栏元素
document.querySelector('#test-selection-bar')

// 检查 Shadow DOM 容器
document.querySelector('#ai-selection-shadow-container')

// 检查当前选择的文本
window.getSelection().toString()

// 手动触发选择事件
const event = new MouseEvent('mouseup', { bubbles: true });
document.dispatchEvent(event);
```
